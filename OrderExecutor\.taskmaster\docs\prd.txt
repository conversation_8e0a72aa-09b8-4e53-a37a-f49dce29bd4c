<context>
# 概述  
OrderExecutor 是為 EA_Wizard MQL4 交易框架設計的簡單直接的訂單執行模組。該模組專注於提供可靠的基本訂單操作功能，包括開倉、平倉和修改訂單，並採用佇列緩衝系統來處理訂單請求。設計理念強調簡潔性和可維護性，避免不必要的複雜功能。

# 核心功能  
## 訂單開倉 (Order Opening)
- 實現 BUY 和 SELL 市價單執行功能
- 提供基本錯誤處理機制
- 支援標準的手數、止損和止盈參數
- 整合現有的 EAOrderManager 組件

## 訂單平倉 (Order Closing)
- 提供完整倉位平倉功能
- 支援部分倉位平倉操作
- 實現按票號平倉和按條件平倉
- 確保平倉操作的可靠性

## 訂單修改 (Order Modification)
- 修改現有訂單的止損價位
- 修改現有訂單的止盈價位
- 調整掛單的開倉價格
- 提供批量修改功能

## 佇列緩衝系統 (Queue Buffer System)
- 實現 FIFO 佇列資料結構管理訂單請求
- 提供非同步訂單處理機制
- 確保執行緒安全的佇列操作
- 支援並發訂單處理

# 使用者體驗  
## 目標使用者
- 使用 EA_Wizard 框架的 EA 開發者
- 需要可靠訂單執行的量化交易者
- 建構可擴展交易系統的系統架構師

## 主要使用流程
1. 交易策略信號 → OrderExecutor 佇列 → 市場執行
2. 訂單請求入隊 → FIFO 處理 → 執行結果回傳
3. 錯誤處理 → 重試機制 → 狀態更新

## 整合要點
- 與現有 EAOrderManager 無縫整合
- 相容 OrderTracker 和 OrderGroup 系統
- 遵循 EA_Wizard 框架設計模式
</context>
<PRD>
# 技術架構  
## 系統組件
- **OrderExecutor**: 核心訂單執行引擎，管理佇列和執行邏輯
- **OrderQueue**: FIFO 佇列實現，處理訂單請求緩衝
- **ExecutionProcessor**: 訂單處理器，負責實際的市場操作
- **ErrorHandler**: 錯誤處理組件，提供重試和錯誤記錄機制

## 資料模型
- **OrderRequest**: 標準化訂單請求結構，包含所有必要參數
- **ExecutionResult**: 執行結果資料結構，記錄成功/失敗狀態
- **QueueItem**: 佇列項目結構，封裝請求和元資料
- **ErrorInfo**: 錯誤資訊結構，記錄錯誤類型和處理狀態

## API 和整合
- 通過委託模式整合現有 EAOrderManager
- 相容 mql4-lib OrderManager 進行底層操作
- 事件驅動架構支援執行回調
- 內部組件間採用簡單介面通信

## 基礎設施需求
- 執行緒安全的佇列操作機制
- 記憶體高效的資料結構設計
- 可配置的日誌和監控功能
- 錯誤恢復和狀態持久化機制

# 開發路線圖  
## 第一階段：核心佇列系統 (MVP)
- 實現基本的 FIFO 佇列資料結構
- 建立 OrderRequest 和 ExecutionResult 資料模型
- 整合現有 EAOrderManager 組件
- 實現基本的錯誤處理和日誌記錄

## 第二階段：訂單執行功能
- 實現市價單開倉功能 (BUY/SELL)
- 添加訂單平倉功能 (完整/部分)
- 實現訂單修改功能 (止損/止盈/價格)
- 強化錯誤處理和重試機制

## 第三階段：佇列處理優化
- 實現非同步訂單處理機制
- 添加執行緒安全的佇列操作
- 優化佇列性能和記憶體使用
- 實現批量處理功能

## 第四階段：系統整合和測試
- 完整的 EA_Wizard 框架整合
- 與 OrderTracker 和 OrderGroup 相容性測試
- 性能優化和穩定性改進
- 完善文檔和使用範例

# 邏輯依賴鏈
1. **基礎結構**: 佇列資料結構和基本資料模型
2. **核心整合**: EAOrderManager 整合和相容性層
3. **訂單功能**: 開倉、平倉、修改訂單的基本實現
4. **佇列處理**: 非同步處理和執行緒安全機制
5. **錯誤處理**: 全面的錯誤管理和重試邏輯
6. **性能優化**: 佇列效率和記憶體管理優化
7. **系統測試**: 整合測試和穩定性驗證
8. **文檔完善**: 使用指南和 API 文檔

# 風險和緩解措施  
## 技術挑戰
- **佇列執行緒安全**: 實現適當的鎖定機制和原子操作
- **記憶體管理**: 使用高效資料結構和適當的清理機制
- **訂單執行延遲**: 優化佇列處理和 API 調用

## MVP 範圍管理
- 從基本佇列和簡單訂單操作開始建立基礎
- 漸進式功能添加，保持向後相容性
- 模組化設計允許獨立組件開發

## 資源限制
- 利用現有 mql4-lib 基礎設施
- 重用 EA_Wizard 框架模式和慣例
- 專注於核心執行可靠性而非高級功能

# 附錄  
## 技術規格
- MQL4/MQL5 相容性，使用範本泛型程式設計
- EA_Wizard 框架整合，遵循既定模式
- 繁體中文文檔和註釋
- 使用 'new' 運算子進行堆積分配以保持一致性
- 遵循單一職責原則的模組化組件設計

## 研究發現
- 分析現有 EAOrderManager 委託模式
- 研究 mql4-lib OrderManager 重試機制
- 檢視 EA_Wizard 組件架構標準
- 佇列資料結構在 MQL4 環境中的最佳實踐
</PRD>
