# Task ID: 2
# Title: Implement OrderRequest Data Model
# Status: pending
# Dependencies: None
# Priority: high
# Description: Define the OrderRequest data structure to encapsulate all necessary parameters for order execution.
# Details:
Create a class or struct in MQL4 to represent an OrderRequest, including fields for order type (BUY/SELL), lot size, stop loss, take profit, and other relevant parameters. Ensure compatibility with the EAOrderManager component.

# Test Strategy:
Unit tests to validate the OrderRequest structure and its serialization/deserialization.
