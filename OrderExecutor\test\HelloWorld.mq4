//+------------------------------------------------------------------+
//|                                                   HelloWorld.mq4 |
//|                                    OrderExecutor Test Script     |
//|                                    EA_Wizard Framework Component |
//+------------------------------------------------------------------+
#property copyright "OrderExecutor Module"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 簡單的 Hello World 測試腳本                                     |
//| 用於驗證 OrderExecutor 專案的基本編譯和建構過程                 |
//+------------------------------------------------------------------+

// 包含主要 OrderExecutor 模組
#include "../src/OrderExecutor.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 輸出測試訊息
    Print("OrderExecutor Hello World Test - 初始化成功");
    Print("專案結構驗證: OrderExecutor 模組已正確載入");
    
    // 測試 OrderExecutor 類別實例化
    OrderExecutor* executor = new OrderExecutor();
    if (executor != NULL)
    {
        Print("OrderExecutor 類別實例化成功");
        
        // 測試初始化方法
        if (executor.Initialize())
        {
            Print("OrderExecutor 初始化方法執行成功");
        }
        else
        {
            Print("OrderExecutor 初始化方法執行失敗");
        }
        
        // 清理資源
        executor.Deinitialize();
        delete executor;
        Print("OrderExecutor 資源清理完成");
    }
    else
    {
        Print("錯誤: OrderExecutor 類別實例化失敗");
        return INIT_FAILED;
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("OrderExecutor Hello World Test - 清理完成");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 此測試腳本不需要 tick 處理
}
